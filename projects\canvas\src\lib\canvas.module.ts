import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

import { CanvasComponent } from './canvas.component';
import { RadioButtonComponent } from './radio-button/radio-button.component';
import { RadioGroupComponent } from './radio-group/radio-group.component';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    CanvasComponent,
    RadioButtonComponent,
    RadioGroupComponent
  ],
  exports: [
    CanvasComponent,
    RadioButtonComponent,
    RadioGroupComponent
  ]
})
export class CanvasModule { }
