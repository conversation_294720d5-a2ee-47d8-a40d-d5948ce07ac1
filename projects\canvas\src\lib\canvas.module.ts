import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

import { CanvasComponent } from './canvas.component';
import { RadioButtonComponent } from './radio-button/radio-button.component';
import { RadioGroupComponent } from './radio-group/radio-group.component';
import { CheckboxComponent } from './checkbox/checkbox.component';
import { CheckboxGroupComponent } from './checkbox-group/checkbox-group.component';

@NgModule({
  imports: [
    CommonModule,
    ReactiveFormsModule,
    CanvasComponent,
    RadioButtonComponent,
    RadioGroupComponent,
    CheckboxComponent,
    CheckboxGroupComponent
  ],
  exports: [
    CanvasComponent,
    RadioButtonComponent,
    RadioGroupComponent,
    CheckboxComponent,
    CheckboxGroupComponent
  ]
})
export class CanvasModule { }
