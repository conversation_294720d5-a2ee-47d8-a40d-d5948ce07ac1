<div class="checkbox-group"
     [class.checkbox-group--disabled]="disabled"
     [class.checkbox-group--error]="hasError"
     [attr.role]="'group'"
     [attr.aria-label]="ariaLabel"
     [attr.aria-labelledby]="ariaLabelledBy"
     [attr.aria-required]="required"
     [attr.aria-invalid]="hasError">
  
  <lib-checkbox *ngIf="showSelectAll"
                [label]="selectAllLabel"
                [checked]="selectAllChecked"
                [indeterminate]="selectAllIndeterminate"
                [disabled]="disabled"
                (checkedChange)="onSelectAllChange($event)"
                class="checkbox-group__select-all">
  </lib-checkbox>
  
  <div class="checkbox-group__items">
    <ng-content></ng-content>
  </div>
  
  <div class="checkbox-group__error-message" *ngIf="hasError && errorMessage">
    {{ errorMessage }}
  </div>
</div>