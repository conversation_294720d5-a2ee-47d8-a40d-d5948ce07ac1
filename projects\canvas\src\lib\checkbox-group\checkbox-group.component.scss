.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-family: inherit;
  padding: 8px 0;

  &--disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  &--error {
    border: 2px solid #e74c3c;
    border-radius: 8px;
    padding: 16px;
    background-color: rgba(231, 76, 60, 0.05);

    ::ng-deep .checkbox__indicator {
      border-color: #e74c3c !important;
    }
  }

  &__select-all {
    margin-bottom: 12px;
    font-weight: 600;
    
    ::ng-deep .checkbox__indicator {
      width: 20px;
      height: 20px;
    }
    
    ::ng-deep .checkbox__text {
      font-size: 16px;
    }
  }

  &__items {
    display: flex;
    flex-direction: column;
    gap: 8px;
    padding-left: 8px;
  }

  &__error-message {
    color: #e74c3c;
    font-size: 0.875rem;
    margin-top: 8px;
    font-weight: 500;
    padding-left: 4px;
  }

  // Horizontal layout option
  &.checkbox-group--horizontal {
    .checkbox-group__items {
      flex-direction: row;
      flex-wrap: wrap;
      gap: 16px;
    }
  }
}