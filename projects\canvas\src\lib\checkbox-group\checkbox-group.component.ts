import {
  Component,
  Input,
  Output,
  EventEmitter,
  forwardRef,
  ContentChildren,
  QueryList,
  AfterContentInit,
  OnDestroy,
  ChangeDetectionStrategy,
  ChangeDetectorRef
} from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { CheckboxComponent } from '../checkbox/checkbox.component';

@Component({
  selector: 'lib-checkbox-group',
  standalone: true,
  imports: [CommonModule, CheckboxComponent],
  templateUrl: './checkbox-group.component.html',
  styleUrls: ['./checkbox-group.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CheckboxGroupComponent),
      multi: true
    }
  ]
})
export class CheckboxGroupComponent implements ControlValueAccessor, AfterContentInit, <PERSON><PERSON><PERSON><PERSON> {
  @Input() name: string = `checkbox-group-${Math.random().toString(36).substring(2, 11)}`;
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() ariaLabel: string = '';
  @Input() ariaLabelledBy: string = '';
  @Input() errorMessage: string = '';
  @Input() showError: boolean = false;
  @Input() showSelectAll: boolean = false;
  @Input() selectAllLabel: string = 'Select All';

  @Output() change = new EventEmitter<any[]>();

  @ContentChildren(CheckboxComponent) checkboxes!: QueryList<CheckboxComponent>;

  selectAllChecked: boolean = false;
  selectAllIndeterminate: boolean = false;

  private _value: any[] = [];
  private _hasError: boolean = false;
  private destroy$ = new Subject<void>();

  // ControlValueAccessor implementation
  private onChange = (value: any) => {};
  private onTouched = () => {};

  constructor(private cdr: ChangeDetectorRef) {}

  get value(): any[] {
    return this._value;
  }

  set value(newValue: any[]) {
    if (this._value !== newValue) {
      this._value = newValue || [];
      this.updateCheckboxes();
      this.updateSelectAllState();
      this.onChange(newValue);
      this.change.emit(newValue);
    }
  }

  get hasError(): boolean {
    return this._hasError || this.showError;
  }

  set hasError(value: boolean) {
    this._hasError = value;
  }

  ngAfterContentInit(): void {
    this.setupCheckboxes();
    
    // Listen for changes in checkboxes
    this.checkboxes.changes
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        this.setupCheckboxes();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  // ControlValueAccessor methods
  writeValue(value: any[]): void {
    this._value = value || [];
    this.updateCheckboxes();
    this.updateSelectAllState();
    this.cdr.markForCheck();
  }

  registerOnChange(fn: any): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: any): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
    this.updateCheckboxes();
    this.cdr.markForCheck();
  }

  // Handle select all checkbox
  onSelectAllChange(checked: boolean): void {
    this.selectAllChecked = checked;
    this.selectAllIndeterminate = false;
    
    if (this.checkboxes) {
      this.checkboxes.forEach(checkbox => {
        if (!checkbox.disabled) {
          checkbox.checked = checked;
          checkbox.markForCheck();
        }
      });
    }
    
    this.updateValue();
    this.onTouched();
  }

  private setupCheckboxes(): void {
    if (this.checkboxes) {
      this.checkboxes.forEach(checkbox => {
        // Set initial state
        checkbox.checked = this._value.includes(checkbox.value);
        checkbox.disabled = this.disabled;
        
        // Subscribe to checkbox changes
        checkbox.checkedChange
          .pipe(takeUntil(this.destroy$))
          .subscribe(() => {
            this.updateValue();
            this.updateSelectAllState();
            this.onTouched();
          });
      });
      
      this.updateSelectAllState();
    }
  }

  private updateCheckboxes(): void {
    if (this.checkboxes) {
      this.checkboxes.forEach(checkbox => {
        checkbox.checked = this._value.includes(checkbox.value);
        checkbox.disabled = this.disabled;
        checkbox.markForCheck();
      });
    }
  }

  private updateValue(): void {
    if (this.checkboxes) {
      const selectedValues = this.checkboxes
        .filter(checkbox => checkbox.checked)
        .map(checkbox => checkbox.value);
      
      this.value = selectedValues;
    }
  }

  private updateSelectAllState(): void {
    if (!this.checkboxes || this.checkboxes.length === 0) {
      this.selectAllChecked = false;
      this.selectAllIndeterminate = false;
      return;
    }

    const enabledCheckboxes = this.checkboxes.filter(checkbox => !checkbox.disabled);
    const checkedCount = enabledCheckboxes.filter(checkbox => checkbox.checked).length;
    
    this.selectAllChecked = checkedCount === enabledCheckboxes.length && enabledCheckboxes.length > 0;
    this.selectAllIndeterminate = checkedCount > 0 && checkedCount < enabledCheckboxes.length;
    this.cdr.markForCheck();
  }

  markAsTouched(): void {
    this.onTouched();
  }
}