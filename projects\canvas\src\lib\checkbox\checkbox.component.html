<div class="checkbox" [class.checkbox--checked]="checked" [class.checkbox--disabled]="disabled" [class.checkbox--indeterminate]="indeterminate">
  <input 
    type="checkbox"
    class="checkbox__input"
    [id]="id"
    [name]="name"
    [checked]="checked"
    [disabled]="disabled"
    [required]="required"
    [attr.aria-checked]="indeterminate ? 'mixed' : checked"
    (change)="onCheckboxChange($event)">
  <label [for]="id" class="checkbox__label">
    <span class="checkbox__indicator"></span>
    <span class="checkbox__text">{{ label }}</span>
  </label>
</div>