.checkbox {
  display: inline-flex;
  align-items: center;
  position: relative;
  cursor: pointer;
  font-family: inherit;
  margin-bottom: 8px;

  &__input {
    position: absolute;
    opacity: 0;
    height: 0;
    width: 0;
    cursor: pointer;
  }

  &__label {
    display: flex;
    align-items: center;
    cursor: pointer;
  }

  &__indicator {
    position: relative;
    display: inline-block;
    width: 18px;
    height: 18px;
    border: 2px solid #666;
    border-radius: 3px;
    margin-right: 8px;
    transition: all 0.2s ease;

    &::after {
      content: '';
      position: absolute;
      display: none;
      left: 5px;
      top: 1px;
      width: 5px;
      height: 10px;
      border: solid white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
    }
  }

  &__text {
    font-size: 14px;
  }

  &--checked {
    .checkbox__indicator {
      background-color: #3498db;
      border-color: #3498db;

      &::after {
        display: block;
      }
    }
  }

  &--indeterminate {
    .checkbox__indicator {
      background-color: #3498db;
      border-color: #3498db;

      &::after {
        display: block;
        left: 3px;
        top: 7px;
        width: 10px;
        height: 0;
        border-width: 0 0 2px 0;
        transform: none;
      }
    }
  }

  &--disabled {
    opacity: 0.6;
    cursor: not-allowed;

    .checkbox__label {
      cursor: not-allowed;
    }
  }

  &:hover:not(.checkbox--disabled) {
    .checkbox__indicator {
      border-color: #3498db;
    }
  }
}