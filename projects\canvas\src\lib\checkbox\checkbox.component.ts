import { 
  Component, 
  Input, 
  Output, 
  EventEmitter, 
  HostBinding,
  ChangeDetectionStrategy,
  ChangeDetectorRef
} from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'lib-checkbox',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './checkbox.component.html',
  styleUrls: ['./checkbox.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CheckboxComponent {
  @Input() value: any;
  @Input() label: string = '';
  @Input() checked: boolean = false;
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() indeterminate: boolean = false;
  @Input() name: string = '';
  @Input() id: string = `checkbox-${Math.random().toString(36).substring(2, 11)}`;

  @Output() checkedChange = new EventEmitter<boolean>();

  @HostBinding('class.checkbox--checked') get isChecked() { return this.checked; }
  @HostBinding('class.checkbox--disabled') get isDisabled() { return this.disabled; }
  @HostBinding('class.checkbox--indeterminate') get isIndeterminate() { return this.indeterminate; }

  constructor(private cdr: ChangeDetectorRef) {}

  onCheckboxChange(event: Event): void {
    event.stopPropagation();
    if (!this.disabled) {
      this.checked = (event.target as HTMLInputElement).checked;
      this.indeterminate = false;
      this.checkedChange.emit(this.checked);
    }
  }

  markForCheck(): void {
    this.cdr.markForCheck();
  }
}