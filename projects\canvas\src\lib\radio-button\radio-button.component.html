<label class="radio-button" 
       [class.radio-button--checked]="checked"
       [class.radio-button--disabled]="disabled">
  <input type="radio"
         class="radio-button__input"
         [name]="name"
         [value]="value"
         [checked]="checked"
         [disabled]="disabled"
         [required]="required"
         [tabindex]="tabIndex"
         [attr.aria-label]="ariaLabel"
         [attr.aria-labelledby]="ariaLabelledBy"
         (change)="onInputChange($event)"
         (focus)="onInputFocus($event)"
         (blur)="onInputBlur($event)">
  
  <span class="radio-button__indicator">
    <span class="radio-button__inner-circle"></span>
  </span>
  
  <span class="radio-button__label">
    <ng-content></ng-content>
  </span>
</label>
