.radio-button {
  display: inline-flex;
  align-items: center;
  cursor: pointer;
  position: relative;
  margin: 0;
  padding: 8px 0;
  font-family: inherit;
  font-size: 14px;
  line-height: 1.4;
  color: #333;
  user-select: none;
  transition: all 0.2s ease;

  &:hover:not(.radio-button--disabled) {
    .radio-button__indicator {
      border-color: #007bff;
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
    }
  }

  &--checked {
    .radio-button__indicator {
      border-color: #007bff;
      background-color: #007bff;
    }

    .radio-button__inner-circle {
      transform: scale(1);
      opacity: 1;
    }
  }

  &--disabled {
    cursor: not-allowed;
    opacity: 0.6;

    .radio-button__indicator {
      border-color: #ccc;
      background-color: #f5f5f5;
    }

    .radio-button__label {
      color: #999;
    }
  }

  &__input {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
    margin: 0;
    padding: 0;
    border: none;

    &:focus + .radio-button__indicator {
      outline: 2px solid #007bff;
      outline-offset: 2px;
    }
  }

  &__indicator {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 50%;
    background-color: #fff;
    transition: all 0.2s ease;
    flex-shrink: 0;
    margin-right: 8px;
  }

  &__inner-circle {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #fff;
    transform: scale(0);
    opacity: 0;
    transition: all 0.2s ease;
  }

  &__label {
    flex: 1;
    margin-left: 0;
  }
}

// Focus styles for accessibility
.radio-button__input:focus-visible + .radio-button__indicator {
  outline: 2px solid #007bff;
  outline-offset: 2px;
}

// High contrast mode support
@media (prefers-contrast: high) {
  .radio-button {
    &__indicator {
      border-width: 3px;
    }

    &--checked .radio-button__indicator {
      background-color: #000;
    }

    &--checked .radio-button__inner-circle {
      background-color: #fff;
    }
  }
}
