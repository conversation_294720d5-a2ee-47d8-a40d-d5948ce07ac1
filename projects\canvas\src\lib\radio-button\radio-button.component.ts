import { Component, Input, Output, EventEmitter, forwardRef, HostListener, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';

@Component({
  selector: 'lib-radio-button',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './radio-button.component.html',
  styleUrls: ['./radio-button.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => RadioButtonComponent),
      multi: true
    }
  ]
})
export class RadioButtonComponent implements ControlValueAccessor {
  @Input() value: any;
  @Input() name: string = '';
  @Input() disabled: boolean = false;
  @Input() required: boolean = false;
  @Input() tabIndex: number = 0;
  @Input() ariaLabel: string = '';
  @Input() ariaLabelledBy: string = '';

  @Output() change = new EventEmitter<any>();
  @Output() focusEvent = new EventEmitter<Event>();
  @Output() blur = new EventEmitter<Event>();

  private _checked: boolean = false;
  private _groupValue: any;

  // ControlValueAccessor implementation
  private onChange = (value: any) => {};
  private onTouched = () => {};

  constructor(public elementRef: ElementRef) {}

  get checked(): boolean {
    return this._checked;
  }

  set checked(value: boolean) {
    if (this._checked !== value) {
      this._checked = value;
      if (value) {
        this.onChange(this.value);
      }
    }
  }

  get groupValue(): any {
    return this._groupValue;
  }

  set groupValue(value: any) {
    this._groupValue = value;
    this._checked = this.value === value;
  }

  // ControlValueAccessor methods
  writeValue(value: any): void {
    this.groupValue = value;
  }

  registerOnChange(fn: (value: any) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled = isDisabled;
  }

  // Event handlers
  onInputChange(event: Event): void {
    event.stopPropagation();
    if (!this.disabled) {
      this.checked = true;
      this.change.emit(this.value);
    }
  }

  onInputFocus(event: Event): void {
    this.focusEvent.emit(event);
  }

  onInputBlur(event: Event): void {
    this.onTouched();
    this.blur.emit(event);
  }

  @HostListener('click', ['$event'])
  onClick(event: Event): void {
    if (!this.disabled) {
      this.elementRef.nativeElement.querySelector('input').focus();
    }
  }

  // Accessibility methods
  focus(): void {
    this.elementRef.nativeElement.querySelector('input').focus();
  }
}
