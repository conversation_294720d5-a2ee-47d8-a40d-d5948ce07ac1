<div class="radio-group"
     [class.radio-group--disabled]="disabled"
     [class.radio-group--error]="hasError"
     [attr.role]="'radiogroup'"
     [attr.aria-label]="ariaLabel"
     [attr.aria-labelledby]="ariaLabelledBy"
     [attr.aria-required]="required"
     [attr.aria-invalid]="hasError">
  <ng-content></ng-content>
  <div class="radio-group__error-message" *ngIf="hasError && errorMessage">
    {{ errorMessage }}
  </div>
</div>
