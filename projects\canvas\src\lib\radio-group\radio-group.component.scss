.radio-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
  font-family: inherit;

  &--disabled {
    opacity: 0.6;
    pointer-events: none;
  }

  &--error {
    border: 2px solid #e74c3c;
    border-radius: 8px;
    padding: 16px;
    background-color: rgba(231, 76, 60, 0.05);

    ::ng-deep .radio-button__indicator {
      border-color: #e74c3c !important;
    }

    ::ng-deep .radio-button:not(.radio-button--checked) .radio-button__label {
      color: #e74c3c;
    }
  }

  // Horizontal layout option
  &.radio-group--horizontal {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 16px;
  }

  // Inline layout option
  &.radio-group--inline {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 24px;
    align-items: center;
  }

  &__error-message {
    color: #e74c3c;
    font-size: 0.875rem;
    margin-top: 8px;
    font-weight: 500;
    padding-left: 4px;
  }
}

// Responsive design
@media (max-width: 768px) {
  .radio-group {
    &.radio-group--horizontal,
    &.radio-group--inline {
      flex-direction: column;
      gap: 8px;
    }
  }
}
