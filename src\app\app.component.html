<div class="app-container">
  <header class="app-header">
    <h1>{{ title }}</h1>
    <p>Demonstrating custom radio group, radio button, checkbox group, and checkbox components with reactive forms</p>
  </header>


  <main class="main-content">
    <form [formGroup]="userForm" (ngSubmit)="onSubmit()" class="demo-form">
      <!-- Basic Information Section -->
      <section class="form-section">
        <h2>Basic Information</h2>

        <div class="form-group">
          <label for="name">Full Name *</label>
          <input
            id="name"
            type="text"
            formControlName="name"
            class="form-input"
            [class.error]="isFieldInvalid('name')"
            placeholder="Enter your full name">
          <div class="error-message" *ngIf="isFieldInvalid('name')">
            {{ getFieldError('name') }}
          </div>
        </div>

        <div class="form-group">
          <label for="email">Email Address *</label>
          <input
            id="email"
            type="email"
            formControlName="email"
            class="form-input"
            [class.error]="isFieldInvalid('email')"
            placeholder="Enter your email address">
          <div class="error-message" *ngIf="isFieldInvalid('email')">
            {{ getFieldError('email') }}
          </div>
        </div>
      </section>

      <!-- Gender Selection Section -->
      <section class="form-section">
        <h2>Gender Selection *</h2>
        <lib-radio-group
          formControlName="gender"
          ariaLabel="Select your gender"
          [showError]="isFieldInvalid('gender')"
          [errorMessage]="getFieldError('gender')">
          <lib-radio-button
            *ngFor="let option of genderOptions"
            [value]="option.value">
            {{ option.label }}
          </lib-radio-button>
        </lib-radio-group>
      </section>

      <!-- Subscription Plan Section -->
      <section class="form-section">
        <h2>Subscription Plan *</h2>
        <lib-radio-group
          formControlName="subscription"
          ariaLabel="Select your subscription plan"
          [showError]="isFieldInvalid('subscription')"
          [disabled]="true"
          [errorMessage]="getFieldError('subscription')">
          <lib-radio-button
            *ngFor="let option of subscriptionOptions"
            [value]="option.value">
            {{ option.label }}
          </lib-radio-button>
        </lib-radio-group>
      </section>

      <!-- Notification Preferences Section -->
      <section class="form-section">
        <h2>Notification Preferences</h2>
        <lib-radio-group
          formControlName="notifications"
          ariaLabel="Select your notification preference">
          <lib-radio-button
            *ngFor="let option of notificationOptions"
            [value]="option.value">
            {{ option.label }}
          </lib-radio-button>
        </lib-radio-group>
      </section>

      <!-- Single Checkbox Examples -->
      <section class="form-section">
        <h2>Single Checkbox Examples</h2>

        <div class="checkbox-examples">
          <lib-checkbox
            formControlName="newsletter"
            label="Subscribe to newsletter">
          </lib-checkbox>

          <lib-checkbox
            formControlName="terms"
            label="I agree to the terms and conditions *"
            [class.error]="isFieldInvalid('terms')">
          </lib-checkbox>
          <div class="error-message" *ngIf="isFieldInvalid('terms')">
            {{ getFieldError('terms') }}
          </div>

          <lib-checkbox
            formControlName="singleCheckbox"
            label="Single checkbox example">
          </lib-checkbox>
        </div>
      </section>

      <!-- Interests Checkbox Group -->
      <section class="form-section">
        <h2>Interests (Select at least 2) *</h2>
        <lib-checkbox-group
          formControlName="interests"
          [showSelectAll]="true"
          selectAllLabel="Select All Interests"
          [showError]="isFieldInvalid('interests')"
          [errorMessage]="getFieldError('interests')"
          ariaLabel="Select your interests">
          <lib-checkbox
            *ngFor="let option of interestOptions"
            [value]="option.value"
            [label]="option.label">
          </lib-checkbox>
        </lib-checkbox-group>
      </section>

      <!-- Skills Checkbox Group -->
      <section class="form-section">
        <h2>Technical Skills</h2>
        <lib-checkbox-group
          formControlName="skills"
          [showSelectAll]="true"
          selectAllLabel="Select All Skills"
          ariaLabel="Select your technical skills">
          <lib-checkbox
            *ngFor="let option of skillOptions"
            [value]="option.value"
            [label]="option.label">
          </lib-checkbox>
        </lib-checkbox-group>
      </section>

      <!-- Form Actions -->
      <section class="form-actions">
        <button type="submit" class="btn btn-primary">Submit Reactive Form</button>
        <button type="button" class="btn btn-secondary" (click)="onReset()">Reset Reactive Form</button>
      </section>

      <!-- Form Debug Info -->
      <section class="form-debug" *ngIf="userForm">
        <h3>Form Debug Information</h3>
        <div class="debug-info">
          <p><strong>Form Valid:</strong> {{ userForm.valid }}</p>
          <p><strong>Form Value:</strong></p>
          <pre>{{ userForm.value | json }}</pre>
          <p><strong>Form Errors:</strong></p>
          <pre>{{ userForm.errors | json }}</pre>
        </div>
      </section>
    </form>

    <!-- Template-Driven Form Section -->
    <section class="template-form-section">
      <h2>Template-Driven Form Example</h2>
      <p>Demonstrating checkbox components without reactive forms</p>

      <form #templateForm="ngForm" (ngSubmit)="onTemplateSubmit()" class="demo-form template-form">
        <div class="form-section">
          <h3>Newsletter & Terms</h3>

          <lib-checkbox
            [(ngModel)]="templateModel.newsletter"
            name="newsletter"
            label="Subscribe to newsletter">
          </lib-checkbox>

          <lib-checkbox
            [(ngModel)]="templateModel.terms"
            name="terms"
            label="I agree to the terms and conditions"
            [required]="true">
          </lib-checkbox>
        </div>

        <div class="form-section">
          <h3>Interests (Template-driven)</h3>
          <lib-checkbox-group
            [(ngModel)]="templateModel.interests"
            name="interests"
            [showSelectAll]="true"
            selectAllLabel="Select All Interests">
            <lib-checkbox
              *ngFor="let option of interestOptions"
              [value]="option.value"
              [label]="option.label">
            </lib-checkbox>
          </lib-checkbox-group>
        </div>

        <div class="form-actions">
          <button type="submit" class="btn btn-primary" [disabled]="!templateForm.valid">
            Submit Template Form
          </button>
          <button type="button" class="btn btn-secondary" (click)="onTemplateReset()">
            Reset Template Form
          </button>
        </div>

        <!-- Template Form Debug -->
        <div class="form-debug">
          <h4>Template Form Debug</h4>
          <div class="debug-info">
            <p><strong>Form Valid:</strong> {{ templateForm.valid }}</p>
            <p><strong>Form Value:</strong></p>
            <pre>{{ templateModel | json }}</pre>
          </div>
        </div>
      </form>
    </section>

    <!-- Standalone Examples -->
    <section class="standalone-section">
      <h2>Standalone Examples (No Forms)</h2>
      <p>Demonstrating checkbox components without any form integration</p>

      <div class="standalone-examples">
        <div class="example-group">
          <h3>Individual Checkboxes</h3>
          <lib-checkbox
            label="Standalone checkbox 1"
            [checked]="false">
          </lib-checkbox>

          <lib-checkbox
            label="Pre-checked checkbox"
            [checked]="true">
          </lib-checkbox>

          <lib-checkbox
            label="Disabled checkbox"
            [disabled]="true">
          </lib-checkbox>

          <lib-checkbox
            label="Indeterminate checkbox"
            [indeterminate]="true">
          </lib-checkbox>
        </div>

        <div class="example-group">
          <h3>Checkbox Group (Standalone)</h3>
          <lib-checkbox-group
            [showSelectAll]="true"
            selectAllLabel="Select All Options">
            <lib-checkbox value="option1" label="Option 1"></lib-checkbox>
            <lib-checkbox value="option2" label="Option 2"></lib-checkbox>
            <lib-checkbox value="option3" label="Option 3" [disabled]="true"></lib-checkbox>
            <lib-checkbox value="option4" label="Option 4"></lib-checkbox>
          </lib-checkbox-group>
        </div>
      </div>
    </section>
  </main>
</div>

<router-outlet />
