<div class="app-container">
  <header class="app-header">
    <h1>{{ title }}</h1>
    <p>Demonstrating custom radio group and radio button components with reactive forms</p>
  </header>


  <main class="main-content">
    <form [formGroup]="userForm" (ngSubmit)="onSubmit()" class="demo-form">
      <!-- Basic Information Section -->
      <section class="form-section">
        <h2>Basic Information</h2>

        <div class="form-group">
          <label for="name">Full Name *</label>
          <input
            id="name"
            type="text"
            formControlName="name"
            class="form-input"
            [class.error]="isFieldInvalid('name')"
            placeholder="Enter your full name">
          <div class="error-message" *ngIf="isFieldInvalid('name')">
            {{ getFieldError('name') }}
          </div>
        </div>

        <div class="form-group">
          <label for="email">Email Address *</label>
          <input
            id="email"
            type="email"
            formControlName="email"
            class="form-input"
            [class.error]="isFieldInvalid('email')"
            placeholder="Enter your email address">
          <div class="error-message" *ngIf="isFieldInvalid('email')">
            {{ getFieldError('email') }}
          </div>
        </div>
      </section>

      <!-- Gender Selection Section -->
      <section class="form-section">
        <h2>Gender Selection *</h2>
        <lib-radio-group
          formControlName="gender"
          ariaLabel="Select your gender"
          [showError]="isFieldInvalid('gender')"
          [errorMessage]="getFieldError('gender')">
          <lib-radio-button
            *ngFor="let option of genderOptions"
            [value]="option.value">
            {{ option.label }}
          </lib-radio-button>
        </lib-radio-group>
      </section>

      <!-- Subscription Plan Section -->
      <section class="form-section">
        <h2>Subscription Plan *</h2>
        <lib-radio-group
          formControlName="subscription"
          ariaLabel="Select your subscription plan"
          [showError]="isFieldInvalid('subscription')"
          [disabled]="true"
          [errorMessage]="getFieldError('subscription')">
          <lib-radio-button
            *ngFor="let option of subscriptionOptions"
            [value]="option.value">
            {{ option.label }}
          </lib-radio-button>
        </lib-radio-group>
      </section>

      <!-- Notification Preferences Section -->
      <section class="form-section">
        <h2>Notification Preferences</h2>
        <lib-radio-group
          formControlName="notifications"
          ariaLabel="Select your notification preference">
          <lib-radio-button
            *ngFor="let option of notificationOptions"
            [value]="option.value">
            {{ option.label }}
          </lib-radio-button>
        </lib-radio-group>
      </section>

      <!-- Form Actions -->
      <section class="form-actions">
        <button type="submit" class="btn btn-primary">Submit Form</button>
        <button type="button" class="btn btn-secondary" (click)="onReset()">Reset Form</button>
      </section>

      <!-- Form Debug Info -->
      <section class="form-debug" *ngIf="userForm">
        <h3>Form Debug Information</h3>
        <div class="debug-info">
          <p><strong>Form Valid:</strong> {{ userForm.valid }}</p>
          <p><strong>Form Value:</strong></p>
          <pre>{{ userForm.value | json }}</pre>
          <p><strong>Form Errors:</strong></p>
          <pre>{{ userForm.errors | json }}</pre>
        </div>
      </section>
    </form>
  </main>
</div>

<router-outlet />
