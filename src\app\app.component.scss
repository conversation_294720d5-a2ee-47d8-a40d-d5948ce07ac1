.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.app-header {
  text-align: center;
  margin-bottom: 40px;

  h1 {
    color: #2c3e50;
    font-size: 2.5rem;
    font-weight: 600;
    margin-bottom: 10px;
  }

  p {
    color: #7f8c8d;
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
  }
}

.main-content {
  max-width: 800px;
  margin: 0 auto;
}

.demo-form {
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.form-section {
  margin-bottom: 40px;

  h2 {
    color: #2c3e50;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    border-bottom: 2px solid #ecf0f1;
    padding-bottom: 10px;
  }
}

.form-group {
  margin-bottom: 25px;

  label {
    display: block;
    color: #34495e;
    font-weight: 500;
    margin-bottom: 8px;
    font-size: 0.95rem;
  }
}

.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  box-sizing: border-box;

  &:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
  }

  &.error {
    border-color: #e74c3c;
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
  }

  &::placeholder {
    color: #95a5a6;
  }
}

.error-message {
  color: #e74c3c;
  font-size: 0.875rem;
  margin-top: 6px;
  font-weight: 500;
}

// Radio group styling is now handled by the component itself

.section-description {
  color: #7f8c8d;
  font-size: 0.95rem;
  margin-bottom: 20px;
  font-style: italic;
}

.toggle-controls {
  margin-bottom: 20px;
}

.btn-toggle {
  background: linear-gradient(135deg, #f39c12, #e67e22);
  color: white;
  padding: 8px 20px;
  font-size: 0.9rem;

  &:hover {
    background: linear-gradient(135deg, #e67e22, #d35400);
  }
}

.form-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 40px;
  padding-top: 30px;
  border-top: 1px solid #ecf0f1;
}

.btn {
  padding: 12px 32px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  }

  &:active {
    transform: translateY(0);
  }
}

.btn-primary {
  background: linear-gradient(135deg, #3498db, #2980b9);
  color: white;

  &:hover {
    background: linear-gradient(135deg, #2980b9, #21618c);
  }
}

.btn-secondary {
  background: linear-gradient(135deg, #95a5a6, #7f8c8d);
  color: white;

  &:hover {
    background: linear-gradient(135deg, #7f8c8d, #6c7b7d);
  }
}

.form-debug {
  margin-top: 40px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #17a2b8;

  h3 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.2rem;
  }

  .debug-info {
    p {
      margin-bottom: 10px;
      color: #6c757d;
    }

    pre {
      background: #e9ecef;
      padding: 12px;
      border-radius: 4px;
      font-size: 0.875rem;
      overflow-x: auto;
      color: #495057;
    }
  }
}

// Responsive design
@media (max-width: 768px) {
  .app-container {
    padding: 10px;
  }

  .demo-form {
    padding: 20px;
  }

  .app-header h1 {
    font-size: 2rem;
  }

  .form-actions {
    flex-direction: column;

    .btn {
      width: 100%;
    }
  }
}