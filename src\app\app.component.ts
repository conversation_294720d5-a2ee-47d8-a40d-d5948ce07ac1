import { Component, OnInit } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { CommonModule } from '@angular/common';
import {
  RadioButtonComponent,
  RadioGroupComponent,
  CheckboxComponent,
  CheckboxGroupComponent
} from 'canvas';

@Component({
  selector: 'app-root',
  standalone: true,
  imports: [
    RouterOutlet,
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    RadioButtonComponent,
    RadioGroupComponent,
    CheckboxComponent,
    CheckboxGroupComponent
  ],
  templateUrl: './app.component.html',
  styleUrl: './app.component.scss'
})
export class AppComponent implements OnInit {
  title = 'Custom Radio & Checkbox Components Demo';

  userForm!: FormGroup;

  // Template-driven form models
  templateModel = {
    newsletter: false,
    terms: false,
    interests: [] as string[]
  };

  // Form options
  genderOptions = [
    { value: 'male', label: 'Male' },
    { value: 'female', label: 'Female' },
    { value: 'other', label: 'Other' },
    { value: 'prefer-not-to-say', label: 'Prefer not to say' }
  ];

  subscriptionOptions = [
    { value: 'basic', label: 'Basic Plan ($9.99/month)' },
    { value: 'premium', label: 'Premium Plan ($19.99/month)' },
    { value: 'enterprise', label: 'Enterprise Plan ($49.99/month)' }
  ];

  notificationOptions = [
    { value: 'email', label: 'Email notifications' },
    { value: 'sms', label: 'SMS notifications' },
    { value: 'push', label: 'Push notifications' },
    { value: 'none', label: 'No notifications' }
  ];

  // Checkbox options
  interestOptions = [
    { value: 'technology', label: 'Technology' },
    { value: 'sports', label: 'Sports' },
    { value: 'music', label: 'Music' },
    { value: 'travel', label: 'Travel' },
    { value: 'food', label: 'Food & Cooking' },
    { value: 'books', label: 'Books & Literature' }
  ];

  skillOptions = [
    { value: 'javascript', label: 'JavaScript' },
    { value: 'typescript', label: 'TypeScript' },
    { value: 'angular', label: 'Angular' },
    { value: 'react', label: 'React' },
    { value: 'vue', label: 'Vue.js' },
    { value: 'nodejs', label: 'Node.js' }
  ];

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.userForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      gender: ['', Validators.required],
      subscription: ['', Validators.required],
      notifications: ['email'], // Default value
      disabledExample: [{value: 'option2', disabled: true}], // Disabled field example

      // Checkbox fields
      newsletter: [false],
      terms: [false, Validators.requiredTrue], // Required checkbox
      interests: [[], this.minSelectedValidator(2)], // At least 2 interests required
      skills: [['javascript', 'angular']], // Pre-selected skills
      singleCheckbox: [false]
    });
  }

  onSubmit(): void {
    console.log('Form submission attempted');
    console.log('Form valid:', this.userForm.valid);
    console.log('Form value:', this.userForm.value);
    console.log('Form errors:', this.getFormErrors());

    if (this.userForm.valid) {
      alert('Form submitted successfully! Check the console for details.');
    } else {
      this.userForm.markAllAsTouched();
      //this.markFormGroupTouched();
      alert('Please fix the validation errors before submitting.');
    }
  }

  onReset(): void {
    this.userForm.reset();
    this.userForm.patchValue({
      notifications: 'email', // Reset to default
      skills: ['javascript', 'angular'] // Reset to default skills
    });

    // Reset template-driven form
    this.templateModel = {
      newsletter: false,
      terms: false,
      interests: []
    };
  }

  private markFormGroupTouched(): void {
    Object.keys(this.userForm.controls).forEach(key => {
      const control = this.userForm.get(key);
      control?.markAsTouched();
    });
  }

  // Helper methods for template
  isFieldInvalid(fieldName: string): boolean {
    const field = this.userForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  getFieldError(fieldName: string): string {
    const field = this.userForm.get(fieldName);
    if (field?.errors) {
      if (field.errors['required']) return `${fieldName} is required`;
      if (field.errors['email']) return 'Please enter a valid email';
      if (field.errors['minlength']) return `${fieldName} must be at least ${field.errors['minlength'].requiredLength} characters`;
    }
    return '';
  }

  getFormErrors(): any {
    const errors: any = {};
    Object.keys(this.userForm.controls).forEach(key => {
      const control = this.userForm.get(key);
      if (control && control.errors) {
        errors[key] = control.errors;
      }
    });
    return errors;
  }

  toggleDisabledState(): void {
    const control = this.userForm.get('disabledExample');
    if (control) {
      if (control.disabled) {
        control.enable();
      } else {
        control.disable();
      }
    }
  }

  // Custom validator for minimum selected items
  minSelectedValidator(min: number) {
    return (control: any) => {
      const value = control.value;
      if (!value || !Array.isArray(value)) {
        return { minSelected: { required: min, actual: 0 } };
      }
      return value.length >= min ? null : { minSelected: { required: min, actual: value.length } };
    };
  }

  // Template-driven form methods
  onTemplateSubmit(): void {
    console.log('Template form submitted:', this.templateModel);
    alert('Template form submitted! Check console for details.');
  }

  onTemplateReset(): void {
    this.templateModel = {
      newsletter: false,
      terms: false,
      interests: []
    };
  }
}
