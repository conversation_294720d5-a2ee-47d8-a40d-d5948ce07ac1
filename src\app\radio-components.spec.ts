import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ReactiveFormsModule, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Component } from '@angular/core';
import { RadioButtonComponent, RadioGroupComponent } from 'canvas';

@Component({
  template: `
    <form [formGroup]="testForm">
      <lib-radio-group 
        formControlName="testField"
        [showError]="showError"
        [errorMessage]="errorMessage">
        <lib-radio-button value="option1">Option 1</lib-radio-button>
        <lib-radio-button value="option2">Option 2</lib-radio-button>
        <lib-radio-button value="option3">Option 3</lib-radio-button>
      </lib-radio-group>
    </form>
  `
})
class TestHostComponent {
  testForm: FormGroup;
  showError = false;
  errorMessage = '';

  constructor(private fb: FormBuilder) {
    this.testForm = this.fb.group({
      testField: ['', Validators.required]
    });
  }
}

describe('Radio Components Integration', () => {
  let component: TestHostComponent;
  let fixture: ComponentFixture<TestHostComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [TestHostComponent],
      imports: [
        ReactiveFormsModule,
        RadioButtonComponent,
        RadioGroupComponent
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(TestHostComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should integrate with reactive forms', () => {
    const radioGroup = fixture.debugElement.query(sel => sel.name === 'lib-radio-group');
    expect(radioGroup).toBeTruthy();
    
    // Initially no value selected
    expect(component.testForm.get('testField')?.value).toBe('');
    expect(component.testForm.valid).toBeFalsy();
  });

  it('should show error state when validation fails', () => {
    component.showError = true;
    component.errorMessage = 'This field is required';
    fixture.detectChanges();

    const radioGroup = fixture.debugElement.query(sel => sel.name === 'lib-radio-group');
    expect(radioGroup.nativeElement.classList).toContain('radio-group--error');
  });

  it('should update form value when radio button is selected', () => {
    const radioButtons = fixture.debugElement.queryAll(sel => sel.name === 'lib-radio-button');
    expect(radioButtons.length).toBe(3);

    // Simulate clicking the first radio button
    const firstRadioInput = radioButtons[0].query(sel => sel.name === 'input');
    firstRadioInput.nativeElement.click();
    fixture.detectChanges();

    expect(component.testForm.get('testField')?.value).toBe('option1');
    expect(component.testForm.valid).toBeTruthy();
  });
});
